#include <QtTest/QtTest>
#include "ui/vulkan_context.h"

class TestVulkanContext : public QObject {
    Q_OBJECT

private slots:
    void initTestCase();
    void testVulkanContextCreation();
    void testVulkanContextProperties();
    void testMemoryTypeQuery();
    void cleanupTestCase();

private:
    std::unique_ptr<Vizion3D::UI::VulkanContext> m_context;
};

void TestVulkanContext::initTestCase() {
    // Test initialization
    qDebug() << "Starting Vulkan context tests";
}

void TestVulkanContext::testVulkanContextCreation() {
    // Test basic Vulkan context creation
    auto result = Vizion3D::UI::VulkanContext::create(true);
    
    QVERIFY2(result.isSuccess(), "Failed to create Vulkan context");
    
    m_context = std::move(result.value());
    
    // Verify basic properties
    QVERIFY(m_context != nullptr);
    QVERIFY(m_context->instance() != VK_NULL_HANDLE);
    QVERIFY(m_context->device() != VK_NULL_HANDLE);
    QVERIFY(m_context->physicalDevice() != VK_NULL_HANDLE);
    QVERIFY(m_context->graphicsQueue() != VK_NULL_HANDLE);
    QVERIFY(m_context->presentQueue() != VK_NULL_HANDLE);
    
    qDebug() << "Vulkan context created successfully";
}

void TestVulkanContext::testVulkanContextProperties() {
    QVERIFY(m_context != nullptr);
    
    // Test device properties
    const auto& properties = m_context->deviceProperties();
    QVERIFY(properties.deviceName[0] != '\0');
    
    qDebug() << "Device:" << properties.deviceName;
    qDebug() << "API Version:" << VK_VERSION_MAJOR(properties.apiVersion)
             << "." << VK_VERSION_MINOR(properties.apiVersion)
             << "." << VK_VERSION_PATCH(properties.apiVersion);
    
    // Test queue family indices
    QVERIFY(m_context->graphicsQueueFamily() != UINT32_MAX);
    QVERIFY(m_context->presentQueueFamily() != UINT32_MAX);
    
    // Test validation layer status
    QVERIFY(m_context->validationEnabled());
}

void TestVulkanContext::testMemoryTypeQuery() {
    QVERIFY(m_context != nullptr);
    
    // Test finding memory type for host visible memory
    auto result = m_context->findMemoryType(
        0xFFFFFFFF, // Accept any memory type
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT
    );
    
    QVERIFY2(result.isSuccess(), "Failed to find host visible memory type");
    
    uint32_t memoryTypeIndex = result.value();
    qDebug() << "Found host visible memory type:" << memoryTypeIndex;

    // Test finding memory type for device local memory
    auto deviceResult = m_context->findMemoryType(
        0xFFFFFFFF, // Accept any memory type
        VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT
    );

    QVERIFY2(deviceResult.isSuccess(), "Failed to find device local memory type");

    uint32_t deviceMemoryTypeIndex = deviceResult.value();
    qDebug() << "Found device local memory type:" << deviceMemoryTypeIndex;
}

void TestVulkanContext::cleanupTestCase() {
    m_context.reset();
    qDebug() << "Vulkan context tests completed";
}

QTEST_MAIN(TestVulkanContext)
#include "test_vulkan_context.moc"
