cmake_minimum_required(VERSION 3.10)
project(QtMemoryManagementClangTidyPlugin)

find_package(Clang QUIET CONFIG)

if(NOT Clang_FOUND)
    message(STATUS "Clang not found, skipping QtMemoryManagementClangTidyPlugin build.")
    return()
endif()

add_library(QtMemoryManagementClangTidyPlugin SHARED
    QtParentChildOwnershipCheck.cpp
)

target_include_directories(QtMemoryManagementClangTidyPlugin PRIVATE
    ${CLANG_INCLUDE_DIRS}
    ${LLVM_INCLUDE_DIRS}
)

target_link_libraries(QtMemoryManagementClangTidyPlugin
    clangAST
    clangASTMatchers
    clangBasic
    clangFrontend
    clangTooling
    clangTidy
)

set_target_properties(QtMemoryManagementClangTidyPlugin PROPERTIES
    CXX_STANDARD 17
    CXX_STANDARD_REQUIRED YES
)
