#ifndef VIZION3D_VULKAN_RENDERER_H
#define VIZION3D_VULKAN_RENDERER_H

#include "utils/result.h"
#include "utils/logger.h"
#include "utils/debug_config.h"
#include "ui/vulkan_context.h"
#include "ui/vulkan_buffer.h"
#include "ui/vulkan_shader.h"
#include <vulkan/vulkan.h>
#include <QMatrix4x4>
#include <QVector3D>
#include <QVector4D>
#include <memory>

namespace Vizion3D::UI {

/**
 * @brief Base class for all Vulkan renderers
 * 
 * This class provides common functionality for Vulkan-based rendering,
 * replacing the OpenGL Renderer base class. It integrates with the
 * existing Result<T> error handling system and VLOG_* logging.
 */
class VulkanRenderer {
public:
    /**
     * @brief Uniform buffer object structure for matrices
     */
    struct UniformBufferObject {
        QMatrix4x4 modelViewProjection;
        QMatrix4x4 modelView;
        QVector4D color;
        float padding[12]; // Ensure 256-byte alignment for UBO
    };

    /**
     * @brief Constructor
     */
    VulkanRenderer();

    /**
     * @brief Virtual destructor
     */
    virtual ~VulkanRenderer();

    // Non-copyable, movable
    VulkanRenderer(const VulkanRenderer&) = delete;
    VulkanRenderer& operator=(const VulkanRenderer&) = delete;
    VulkanRenderer(VulkanRenderer&& other) noexcept;
    VulkanRenderer& operator=(VulkanRenderer&& other) noexcept;

    /**
     * @brief Initialize the renderer with Vulkan context
     * @param context The Vulkan context
     * @return Result indicating success or failure
     */
    virtual Vizion3D::Utils::Result<void> initialize(VulkanContext* context) = 0;

    /**
     * @brief Render using Vulkan command buffer
     * @param viewMatrix The view matrix
     * @param projectionMatrix The projection matrix
     * @param commandBuffer The Vulkan command buffer to record into
     * @return Result indicating success or failure
     */
    virtual Vizion3D::Utils::Result<void> render(const QMatrix4x4& viewMatrix, 
                                                 const QMatrix4x4& projectionMatrix,
                                                 VkCommandBuffer commandBuffer) = 0;

    /**
     * @brief Check if the renderer is initialized
     * @return True if initialized, false otherwise
     */
    bool isInitialized() const { return m_initialized; }

protected:
    /**
     * @brief Create a buffer with specified properties
     * @param size Size of the buffer in bytes
     * @param usage Buffer usage flags
     * @param properties Memory property flags
     * @param buffer Output buffer object
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> createBuffer(VkDeviceSize size, 
                                              VkBufferUsageFlags usage, 
                                              VkMemoryPropertyFlags properties, 
                                              VulkanBuffer& buffer);

    /**
     * @brief Create a graphics pipeline
     * @param createInfo Pipeline creation parameters
     * @param pipeline Output pipeline object
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> createGraphicsPipeline(const VulkanShader::PipelineCreateInfo& createInfo,
                                                         std::unique_ptr<VulkanShader>& pipeline);

    /**
     * @brief Create descriptor set layout for uniform buffers
     * @param bindingCount Number of bindings
     * @param descriptorSetLayout Output descriptor set layout
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> createDescriptorSetLayout(uint32_t bindingCount,
                                                           VkDescriptorSetLayout& descriptorSetLayout);

    /**
     * @brief Create descriptor pool
     * @param maxSets Maximum number of descriptor sets
     * @param descriptorPool Output descriptor pool
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> createDescriptorPool(uint32_t maxSets,
                                                       VkDescriptorPool& descriptorPool);

    /**
     * @brief Update uniform buffer with current matrices
     * @param viewMatrix The view matrix
     * @param projectionMatrix The projection matrix
     * @param color Optional color override
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> updateUniformBuffer(const QMatrix4x4& viewMatrix,
                                                      const QMatrix4x4& projectionMatrix,
                                                      const QVector4D& color = QVector4D(1.0f, 1.0f, 1.0f, 1.0f));

    // Vulkan context and device handles
    VulkanContext* m_context = nullptr;
    VkDevice m_device = VK_NULL_HANDLE;
    VkPhysicalDevice m_physicalDevice = VK_NULL_HANDLE;

    // Common resources
    VulkanBuffer m_uniformBuffer;
    VkDescriptorSetLayout m_descriptorSetLayout = VK_NULL_HANDLE;
    VkDescriptorPool m_descriptorPool = VK_NULL_HANDLE;
    VkDescriptorSet m_descriptorSet = VK_NULL_HANDLE;

    // State tracking
    bool m_initialized = false;

private:
    /**
     * @brief Clean up Vulkan resources
     */
    void cleanup();
};

} // namespace Vizion3D::UI

#endif // VIZION3D_VULKAN_RENDERER_H
