#ifndef VIZION3D_VULKAN_BUFFER_H
#define VIZION3D_VULKAN_BUFFER_H

#include "utils/result.h"
#include "utils/logger.h"
#include "utils/debug_config.h"
#include <vulkan/vulkan.h>
#include <QString>

namespace Vizion3D::UI {

// Forward declaration
class VulkanContext;

/**
 * @brief RAII wrapper for Vulkan buffers
 * 
 * This class provides automatic memory management for Vulkan buffers,
 * including creation, mapping, and cleanup. It integrates with the
 * existing Result<T> error handling system.
 */
class VulkanBuffer {
public:
    /**
     * @brief Default constructor - creates an empty buffer
     */
    VulkanBuffer();

    /**
     * @brief Destructor - automatically cleans up resources
     */
    ~VulkanBuffer();

    // Non-copyable, movable
    VulkanBuffer(const VulkanBuffer&) = delete;
    VulkanBuffer& operator=(const VulkanBuffer&) = delete;
    VulkanBuffer(VulkanBuffer&& other) noexcept;
    VulkanBuffer& operator=(VulkanBuffer&& other) noexcept;

    /**
     * @brief Create a buffer with the specified properties
     * @param context The Vulkan context
     * @param size Size of the buffer in bytes
     * @param usage Buffer usage flags
     * @param properties Memory property flags
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> create(const VulkanContext* context,
                                        VkDeviceSize size,
                                        VkBufferUsageFlags usage,
                                        VkMemoryPropertyFlags properties);

    /**
     * @brief Update buffer data
     * @param data Pointer to the data to copy
     * @param size Size of the data in bytes
     * @param offset Offset in the buffer (default: 0)
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> updateData(const void* data, VkDeviceSize size, VkDeviceSize offset = 0);

    /**
     * @brief Map buffer memory for CPU access
     * @param offset Offset in the buffer (default: 0)
     * @param size Size to map (VK_WHOLE_SIZE for entire buffer)
     * @return Result containing pointer to mapped memory or error
     */
    Vizion3D::Utils::Result<void*> map(VkDeviceSize offset = 0, VkDeviceSize size = VK_WHOLE_SIZE);

    /**
     * @brief Unmap buffer memory
     */
    void unmap();

    /**
     * @brief Copy data from another buffer
     * @param srcBuffer Source buffer
     * @param size Size to copy (VK_WHOLE_SIZE for entire buffer)
     * @param srcOffset Offset in source buffer (default: 0)
     * @param dstOffset Offset in destination buffer (default: 0)
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> copyFrom(const VulkanBuffer& srcBuffer,
                                          VkDeviceSize size = VK_WHOLE_SIZE,
                                          VkDeviceSize srcOffset = 0,
                                          VkDeviceSize dstOffset = 0);

    // Accessors
    VkBuffer buffer() const { return m_buffer; }
    VkDeviceMemory memory() const { return m_memory; }
    VkDeviceSize size() const { return m_size; }
    bool isValid() const { return m_buffer != VK_NULL_HANDLE; }
    bool isMapped() const { return m_mappedMemory != nullptr; }

    /**
     * @brief Get buffer descriptor info for descriptor sets
     */
    VkDescriptorBufferInfo getDescriptorInfo(VkDeviceSize offset = 0, VkDeviceSize range = VK_WHOLE_SIZE) const;

private:
    /**
     * @brief Clean up all resources
     */
    void cleanup();

    /**
     * @brief Find suitable memory type for buffer
     * @param typeFilter Memory type filter
     * @param properties Required memory properties
     * @return Result containing memory type index or error
     */
    Vizion3D::Utils::Result<uint32_t> findMemoryType(uint32_t typeFilter, VkMemoryPropertyFlags properties) const;

    // Vulkan objects
    VkDevice m_device = VK_NULL_HANDLE;
    VkBuffer m_buffer = VK_NULL_HANDLE;
    VkDeviceMemory m_memory = VK_NULL_HANDLE;
    
    // Buffer properties
    VkDeviceSize m_size = 0;
    VkBufferUsageFlags m_usage = 0;
    VkMemoryPropertyFlags m_memoryProperties = 0;
    
    // Memory mapping
    void* m_mappedMemory = nullptr;
    
    // Context reference for memory operations
    const VulkanContext* m_context = nullptr;
};

/**
 * @brief Helper function to create a staging buffer for data transfer
 * @param context The Vulkan context
 * @param size Size of the buffer
 * @return Result containing the staging buffer or error
 */
Vizion3D::Utils::Result<VulkanBuffer> createStagingBuffer(const VulkanContext* context, VkDeviceSize size);

/**
 * @brief Helper function to create a vertex buffer
 * @param context The Vulkan context
 * @param size Size of the buffer
 * @return Result containing the vertex buffer or error
 */
Vizion3D::Utils::Result<VulkanBuffer> createVertexBuffer(const VulkanContext* context, VkDeviceSize size);

/**
 * @brief Helper function to create an index buffer
 * @param context The Vulkan context
 * @param size Size of the buffer
 * @return Result containing the index buffer or error
 */
Vizion3D::Utils::Result<VulkanBuffer> createIndexBuffer(const VulkanContext* context, VkDeviceSize size);

/**
 * @brief Helper function to create a uniform buffer
 * @param context The Vulkan context
 * @param size Size of the buffer
 * @return Result containing the uniform buffer or error
 */
Vizion3D::Utils::Result<VulkanBuffer> createUniformBuffer(const VulkanContext* context, VkDeviceSize size);

} // namespace Vizion3D::UI

#endif // VIZION3D_VULKAN_BUFFER_H
