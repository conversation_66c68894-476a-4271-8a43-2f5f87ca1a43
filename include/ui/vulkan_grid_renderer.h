#ifndef VIZION3D_VULKAN_GRID_RENDERER_H
#define VIZION3D_VULKAN_GRID_RENDERER_H

#include "ui/vulkan_renderer.h"
#include <QVector3D>
#include <QVector>
#include <memory>

namespace Vizion3D::UI {

/**
 * @brief Vulkan-based renderer for the grid and coordinate axes
 * 
 * This class renders the grid lines, main coordinate axes, and toolpath area
 * highlighting using Vulkan. It replaces the OpenGL GridRenderer with
 * equivalent functionality.
 */
class VulkanGridRenderer : public VulkanRenderer {
public:
    /**
     * @brief Constructor
     */
    VulkanGridRenderer();

    /**
     * @brief Destructor
     */
    ~VulkanGridRenderer() override;

    /**
     * @brief Initialize the renderer with Vulkan context
     * @param context The Vulkan context
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> initialize(VulkanContext* context) override;

    /**
     * @brief Render the grid using Vulkan command buffer
     * @param viewMatrix The view matrix
     * @param projectionMatrix The projection matrix
     * @param commandBuffer The Vulkan command buffer to record into
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> render(const QMatrix4x4& viewMatrix, 
                                        const QMatrix4x4& projectionMatrix,
                                        VkCommandBuffer commandBuffer) override;

    /**
     * @brief Set the grid size
     * @param size The grid size
     */
    void setSize(float size);

    /**
     * @brief Set the grid step
     * @param step The grid step
     */
    void setStep(float step);

    /**
     * @brief Set the grid color
     * @param color The grid color
     */
    void setColor(const QVector3D& color);

    /**
     * @brief Set the axis colors
     * @param xColor The X axis color
     * @param yColor The Y axis color
     * @param zColor The Z axis color
     */
    void setAxisColors(const QVector3D& xColor, const QVector3D& yColor, const QVector3D& zColor);

    /**
     * @brief Set whether to highlight the toolpath area
     * @param highlight Whether to highlight the toolpath area
     */
    void setHighlightToolpathArea(bool highlight);

    /**
     * @brief Set the toolpath area bounds
     * @param min Minimum corner of the toolpath area
     * @param max Maximum corner of the toolpath area
     */
    void setToolpathAreaBounds(const QVector3D& min, const QVector3D& max);

private:
    /**
     * @brief Generate grid vertices and colors
     */
    void generateGridGeometry();

    /**
     * @brief Generate main axes vertices and colors
     */
    void generateAxesGeometry();

    /**
     * @brief Generate toolpath area vertices and colors
     */
    void generateToolpathAreaGeometry();

    /**
     * @brief Create graphics pipeline for grid rendering
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> createGraphicsPipeline();

    /**
     * @brief Setup vertex buffers for grid data
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> setupGridBuffers();

    /**
     * @brief Setup vertex buffers for axes data
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> setupAxesBuffers();

    /**
     * @brief Setup vertex buffers for toolpath area data
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> setupToolpathAreaBuffers();

    // Grid properties
    float m_size = 200.0f;
    float m_step = 10.0f;
    QVector3D m_color = QVector3D(0.3f, 0.3f, 0.3f);
    QVector3D m_xAxisColor = QVector3D(1.0f, 0.0f, 0.0f);
    QVector3D m_yAxisColor = QVector3D(0.0f, 1.0f, 0.0f);
    QVector3D m_zAxisColor = QVector3D(0.0f, 0.0f, 1.0f);
    bool m_highlightToolpathArea = true;
    QVector3D m_toolpathAreaMin = QVector3D(0.0f, 0.0f, 0.0f);
    QVector3D m_toolpathAreaMax = QVector3D(25.0f, 0.0f, 25.0f);

    // Geometry data
    QVector<QVector3D> m_gridVertices;
    QVector<QVector3D> m_gridColors;
    QVector<QVector3D> m_axesVertices;
    QVector<QVector3D> m_axesColors;
    QVector<QVector3D> m_toolpathAreaVertices;
    QVector<QVector3D> m_toolpathAreaColors;

    // Vulkan resources
    std::unique_ptr<VulkanShader> m_pipeline;
    VulkanBuffer m_gridVertexBuffer;
    VulkanBuffer m_gridColorBuffer;
    VulkanBuffer m_axesVertexBuffer;
    VulkanBuffer m_axesColorBuffer;
    VulkanBuffer m_toolpathAreaVertexBuffer;
    VulkanBuffer m_toolpathAreaColorBuffer;

    // State tracking
    bool m_needsUpdate = true;
};

} // namespace Vizion3D::UI

#endif // VIZION3D_VULKAN_GRID_RENDERER_H
