#ifndef VIZION3D_VULKAN_SHADER_H
#define VIZION3D_VULKAN_SHADER_H

#include "utils/result.h"
#include "utils/logger.h"
#include "utils/debug_config.h"
#include <vulkan/vulkan.h>
#include <QString>
#include <QStringList>
#include <vector>
#include <memory>

namespace Vizion3D::UI {

// Forward declaration
class VulkanContext;

/**
 * @brief RAII wrapper for Vulkan shader modules and pipelines
 * 
 * This class manages SPIR-V shader loading, compilation, and pipeline creation.
 * It integrates with the existing Result<T> error handling system and provides
 * automatic resource cleanup.
 */
class VulkanShader {
public:
    /**
     * @brief Vertex input binding description
     */
    struct VertexInputBinding {
        uint32_t binding;
        uint32_t stride;
        VkVertexInputRate inputRate;
    };

    /**
     * @brief Vertex input attribute description
     */
    struct VertexInputAttribute {
        uint32_t location;
        uint32_t binding;
        VkFormat format;
        uint32_t offset;
    };

    /**
     * @brief Pipeline creation parameters
     */
    struct PipelineCreateInfo {
        std::vector<uint32_t> vertexShaderSpirv;
        std::vector<uint32_t> fragmentShaderSpirv;
        std::vector<VertexInputBinding> vertexBindings;
        std::vector<VertexInputAttribute> vertexAttributes;
        VkPrimitiveTopology topology = VK_PRIMITIVE_TOPOLOGY_TRIANGLE_LIST;
        VkPolygonMode polygonMode = VK_POLYGON_MODE_FILL;
        VkCullModeFlags cullMode = VK_CULL_MODE_BACK_BIT;
        VkFrontFace frontFace = VK_FRONT_FACE_COUNTER_CLOCKWISE;
        bool depthTestEnable = true;
        bool depthWriteEnable = true;
        VkCompareOp depthCompareOp = VK_COMPARE_OP_LESS;
        bool blendEnable = false;
        VkDescriptorSetLayout descriptorSetLayout = VK_NULL_HANDLE;
        VkRenderPass renderPass = VK_NULL_HANDLE;
        uint32_t subpass = 0;
    };

    /**
     * @brief Default constructor
     */
    VulkanShader();

    /**
     * @brief Destructor - automatically cleans up resources
     */
    ~VulkanShader();

    // Non-copyable, movable
    VulkanShader(const VulkanShader&) = delete;
    VulkanShader& operator=(const VulkanShader&) = delete;
    VulkanShader(VulkanShader&& other) noexcept;
    VulkanShader& operator=(VulkanShader&& other) noexcept;

    /**
     * @brief Create graphics pipeline from shader files
     * @param context The Vulkan context
     * @param createInfo Pipeline creation parameters
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> createGraphicsPipeline(const VulkanContext* context,
                                                         const PipelineCreateInfo& createInfo);

    /**
     * @brief Load SPIR-V shader from file
     * @param filePath Path to the SPIR-V shader file
     * @return Result containing shader bytecode or error
     */
    static Vizion3D::Utils::Result<std::vector<uint32_t>> loadShaderFromFile(const QString& filePath);

    /**
     * @brief Create shader module from SPIR-V bytecode
     * @param context The Vulkan context
     * @param spirvCode SPIR-V bytecode
     * @return Result containing shader module or error
     */
    static Vizion3D::Utils::Result<VkShaderModule> createShaderModule(const VulkanContext* context,
                                                                      const std::vector<uint32_t>& spirvCode);

    // Accessors
    VkPipeline pipeline() const { return m_pipeline; }
    VkPipelineLayout layout() const { return m_pipelineLayout; }
    bool isValid() const { return m_pipeline != VK_NULL_HANDLE; }

    /**
     * @brief Bind the pipeline to a command buffer
     * @param commandBuffer The command buffer to bind to
     */
    void bind(VkCommandBuffer commandBuffer) const;

private:
    /**
     * @brief Create pipeline layout
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> createPipelineLayout();

    /**
     * @brief Create the graphics pipeline
     * @param createInfo Pipeline creation parameters
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> createPipeline(const PipelineCreateInfo& createInfo);

    /**
     * @brief Clean up all resources
     */
    void cleanup();

    /**
     * @brief Convert vertex input bindings to Vulkan format
     */
    std::vector<VkVertexInputBindingDescription> convertVertexBindings(
        const std::vector<VertexInputBinding>& bindings) const;

    /**
     * @brief Convert vertex input attributes to Vulkan format
     */
    std::vector<VkVertexInputAttributeDescription> convertVertexAttributes(
        const std::vector<VertexInputAttribute>& attributes) const;

    // Vulkan objects
    VkDevice m_device = VK_NULL_HANDLE;
    VkPipeline m_pipeline = VK_NULL_HANDLE;
    VkPipelineLayout m_pipelineLayout = VK_NULL_HANDLE;
    VkShaderModule m_vertexShader = VK_NULL_HANDLE;
    VkShaderModule m_fragmentShader = VK_NULL_HANDLE;
    
    // Context reference
    const VulkanContext* m_context = nullptr;
};

/**
 * @brief Helper function to create a simple vertex input description for position + color
 * @return Vertex input bindings and attributes for position (vec3) + color (vec3)
 */
std::pair<std::vector<VulkanShader::VertexInputBinding>, 
          std::vector<VulkanShader::VertexInputAttribute>> 
createPositionColorVertexInput();

/**
 * @brief Helper function to create vertex input description for position only
 * @return Vertex input bindings and attributes for position (vec3) only
 */
std::pair<std::vector<VulkanShader::VertexInputBinding>, 
          std::vector<VulkanShader::VertexInputAttribute>> 
createPositionOnlyVertexInput();

} // namespace Vizion3D::UI

#endif // VIZION3D_VULKAN_SHADER_H
