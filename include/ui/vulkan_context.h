#ifndef VIZION3D_VULKAN_CONTEXT_H
#define VIZION3D_VULKAN_CONTEXT_H

#include "utils/result.h"
#include "utils/logger.h"
#include "utils/debug_config.h"
#include <vulkan/vulkan.h>
#include <QString>
#include <QStringList>
#include <memory>
#include <vector>

namespace Vizion3D::UI {

/**
 * @brief Vulkan context management class
 * 
 * This class manages the Vulkan instance, physical device, logical device,
 * and queues. It provides RAII-based resource management and integrates
 * with the existing Result<T> error handling system.
 */
class VulkanContext {
public:
    /**
     * @brief Create a new VulkanContext instance
     * @param enableValidation Whether to enable Vulkan validation layers
     * @return Result containing the VulkanContext or an error
     */
    static Vizion3D::Utils::Result<std::unique_ptr<VulkanContext>> create(bool enableValidation = true);

    /**
     * @brief Destructor - cleans up all Vulkan resources
     */
    ~VulkanContext();

    // Non-copyable, movable
    VulkanContext(const VulkanContext&) = delete;
    VulkanContext& operator=(const VulkanContext&) = delete;
    VulkanContext(VulkanContext&& other) noexcept;
    VulkanContext& operator=(VulkanContext&& other) noexcept;

    // Accessors
    VkInstance instance() const { return m_instance; }
    VkDevice device() const { return m_device; }
    VkPhysicalDevice physicalDevice() const { return m_physicalDevice; }
    VkQueue graphicsQueue() const { return m_graphicsQueue; }
    VkQueue presentQueue() const { return m_presentQueue; }
    uint32_t graphicsQueueFamily() const { return m_graphicsQueueFamily; }
    uint32_t presentQueueFamily() const { return m_presentQueueFamily; }

    /**
     * @brief Get the physical device properties
     */
    const VkPhysicalDeviceProperties& deviceProperties() const { return m_deviceProperties; }

    /**
     * @brief Get the physical device features
     */
    const VkPhysicalDeviceFeatures& deviceFeatures() const { return m_deviceFeatures; }

    /**
     * @brief Check if validation layers are enabled
     */
    bool validationEnabled() const { return m_validationEnabled; }

    /**
     * @brief Find memory type index for given requirements
     * @param typeFilter Memory type filter
     * @param properties Required memory properties
     * @return Result containing the memory type index or an error
     */
    Vizion3D::Utils::Result<uint32_t> findMemoryType(uint32_t typeFilter, 
                                                     VkMemoryPropertyFlags properties) const;

private:
    VulkanContext() = default;

    /**
     * @brief Initialize the Vulkan context
     * @param enableValidation Whether to enable validation layers
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> initialize(bool enableValidation);

    /**
     * @brief Create the Vulkan instance
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> createInstance();

    /**
     * @brief Setup debug messenger for validation layers
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> setupDebugMessenger();

    /**
     * @brief Select a suitable physical device
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> selectPhysicalDevice();

    /**
     * @brief Create the logical device
     * @return Result indicating success or failure
     */
    Vizion3D::Utils::Result<void> createLogicalDevice();

    /**
     * @brief Check if validation layers are available
     * @return True if all required validation layers are available
     */
    bool checkValidationLayerSupport() const;

    /**
     * @brief Get required extensions for the instance
     * @return List of required extension names
     */
    QStringList getRequiredExtensions() const;

    /**
     * @brief Check if a physical device is suitable
     * @param device The physical device to check
     * @return True if the device is suitable
     */
    bool isDeviceSuitable(VkPhysicalDevice device) const;

    /**
     * @brief Find queue families for a physical device
     * @param device The physical device
     * @return Queue family indices
     */
    struct QueueFamilyIndices {
        uint32_t graphicsFamily = UINT32_MAX;
        uint32_t presentFamily = UINT32_MAX;
        
        bool isComplete() const {
            return graphicsFamily != UINT32_MAX && presentFamily != UINT32_MAX;
        }
    };
    QueueFamilyIndices findQueueFamilies(VkPhysicalDevice device) const;

    /**
     * @brief Clean up all Vulkan resources
     */
    void cleanup();

    // Vulkan objects
    VkInstance m_instance = VK_NULL_HANDLE;
    VkDevice m_device = VK_NULL_HANDLE;
    VkPhysicalDevice m_physicalDevice = VK_NULL_HANDLE;
    VkQueue m_graphicsQueue = VK_NULL_HANDLE;
    VkQueue m_presentQueue = VK_NULL_HANDLE;
    
    // Queue family indices
    uint32_t m_graphicsQueueFamily = UINT32_MAX;
    uint32_t m_presentQueueFamily = UINT32_MAX;
    
    // Device properties and features
    VkPhysicalDeviceProperties m_deviceProperties{};
    VkPhysicalDeviceFeatures m_deviceFeatures{};
    VkPhysicalDeviceMemoryProperties m_memoryProperties{};
    
    // Debug support
    VkDebugUtilsMessengerEXT m_debugMessenger = VK_NULL_HANDLE;
    bool m_validationEnabled = false;
    
    // Required validation layers
    static const QStringList s_validationLayers;
    
    // Required device extensions
    static const QStringList s_deviceExtensions;
};

} // namespace Vizion3D::UI

#endif // VIZION3D_VULKAN_CONTEXT_H
