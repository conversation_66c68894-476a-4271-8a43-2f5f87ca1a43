cmake_minimum_required(VERSION 3.16)

# Project name and version
project(Vizion3D VERSION 1.0.0 LANGUAGES CXX)

# Set C++ standard
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Generate compile_commands.json for IDE support (VSCode IntelliSense)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

# Add compiler flags
# Note: We're not silencing OpenGL deprecation warnings anymore
# to ensure we're using modern OpenGL Core Profile correctly

# Debug configuration options
option(VIZION3D_ENABLE_DEBUG "Enable debug mode" ON)
option(VIZION3D_ENABLE_LOGGING "Enable logging" ON)
option(VIZION3D_ENABLE_PROFILING "Enable performance profiling" ON)
option(VIZION3D_ENABLE_ASSERTIONS "Enable assertions" ON)

# Set debug-related compile definitions
if(CMAKE_BUILD_TYPE STREQUAL "Debug" OR VIZION3D_ENABLE_DEBUG)
    add_compile_definitions(VIZION3D_DEBUG_MODE)
endif()

if(NOT VIZION3D_ENABLE_LOGGING)
    add_compile_definitions(VIZION3D_DISABLE_LOGGING)
endif()

if(NOT VIZION3D_ENABLE_PROFILING)
    add_compile_definitions(VIZION3D_DISABLE_PROFILING)
endif()

if(NOT VIZION3D_ENABLE_ASSERTIONS)
    add_compile_definitions(VIZION3D_DISABLE_ASSERTIONS)
endif()

# Include CMake modules
include(CMakePackageConfigHelpers)
include(GNUInstallDirs)

# Find Qt packages
find_package(Qt6 6.2 COMPONENTS Core Gui Widgets OpenGL OpenGLWidgets QUIET)
if(NOT Qt6_FOUND)
    message(STATUS "Qt6 not found, trying Qt5...")
    find_package(Qt5 5.15 COMPONENTS Core Gui Widgets OpenGL REQUIRED)
    set(QT_VERSION 5)
else()
    set(QT_VERSION 6)
endif()

# Find Vulkan SDK
find_package(Vulkan REQUIRED)
if(Vulkan_FOUND)
    message(STATUS "Found Vulkan SDK: ${Vulkan_VERSION}")
    message(STATUS "Vulkan Include Dir: ${Vulkan_INCLUDE_DIRS}")
    message(STATUS "Vulkan Libraries: ${Vulkan_LIBRARIES}")
else()
    message(FATAL_ERROR "Vulkan SDK not found. Please install Vulkan SDK.")
endif()

# Platform-specific Vulkan configuration
if(APPLE)
    # MoltenVK support for macOS
    find_library(MOLTENVK_LIBRARY MoltenVK
        PATHS
            /opt/homebrew/lib
            /usr/local/lib
        NO_DEFAULT_PATH
    )
    if(MOLTENVK_LIBRARY)
        message(STATUS "Found MoltenVK: ${MOLTENVK_LIBRARY}")
        add_compile_definitions(VK_USE_PLATFORM_MACOS_MVK)
    else()
        message(WARNING "MoltenVK not found. Vulkan may not work on macOS.")
    endif()
elseif(WIN32)
    add_compile_definitions(VK_USE_PLATFORM_WIN32_KHR)
elseif(UNIX)
    add_compile_definitions(VK_USE_PLATFORM_XCB_KHR)
endif()

# Find SPIR-V tools for shader compilation
find_program(GLSLC_EXECUTABLE glslc
    HINTS
        ${Vulkan_GLSLC_EXECUTABLE}
        /opt/homebrew/bin
        /usr/local/bin
)
find_program(GLSLANGVALIDATOR_EXECUTABLE glslangValidator
    HINTS
        ${Vulkan_GLSLANG_VALIDATOR_EXECUTABLE}
        /opt/homebrew/bin
        /usr/local/bin
)

if(GLSLC_EXECUTABLE)
    message(STATUS "Found glslc: ${GLSLC_EXECUTABLE}")
    set(SHADER_COMPILER ${GLSLC_EXECUTABLE})
    set(SHADER_COMPILER_TYPE "glslc")
elseif(GLSLANGVALIDATOR_EXECUTABLE)
    message(STATUS "Found glslangValidator: ${GLSLANGVALIDATOR_EXECUTABLE}")
    set(SHADER_COMPILER ${GLSLANGVALIDATOR_EXECUTABLE})
    set(SHADER_COMPILER_TYPE "glslangValidator")
else()
    message(WARNING "No SPIR-V shader compiler found. Shader compilation may not work.")
endif()

# Set automoc, autorcc, and autouic
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# Set UI file search paths
set(CMAKE_AUTOUIC_SEARCH_PATHS ${CMAKE_CURRENT_SOURCE_DIR}/resources/ui)

# Set output directories
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/lib)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# Add include directories
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
)

# Find OpenCASCADE 7.8.0 (required for material removal simulation)
if(APPLE)
    # macOS - try Homebrew and MacPorts locations
    find_package(OpenCASCADE 7.8.0 EXACT QUIET PATHS
        /usr/local/opt/opencascade/lib/cmake/opencascade
        /usr/local/opt/opencascade@7.8.0/lib/cmake/opencascade
        /opt/local/lib/cmake/opencascade
    )
elseif(WIN32)
    # Windows - try vcpkg location
    find_package(OpenCASCADE 7.8.0 EXACT QUIET PATHS
        ${CMAKE_CURRENT_SOURCE_DIR}/../libs/opencascade
        $ENV{VCPKG_ROOT}/installed/x64-windows/share/opencascade
    )
else()
    # Linux - try system location
    find_package(OpenCASCADE 7.8.0 EXACT QUIET)
endif()

if(OpenCASCADE_FOUND)
    if(OpenCASCADE_VERSION VERSION_EQUAL "7.8.0")
        message(STATUS "Found OpenCASCADE 7.8.0: ${OpenCASCADE_INCLUDE_DIR}")
        add_definitions(-DUSE_OPENCASCADE)
        add_definitions(-DOPENCASCADE_VERSION=7.8.0)
        include_directories(${OpenCASCADE_INCLUDE_DIR})
    else()
        message(WARNING "Found OpenCASCADE ${OpenCASCADE_VERSION}, but version 7.8.0 is required. Material removal simulation will be disabled.")
        add_definitions(-DNO_OPENCASCADE)
    endif()
else()
    message(WARNING "OpenCASCADE 7.8.0 not found. Material removal simulation will be disabled.")
    add_definitions(-DNO_OPENCASCADE)
endif()

# Define source files
set(SOURCES
    src/main.cpp
    src/ui/mainwindow.cpp
    src/ui/simulation_view.cpp
    src/ui/project_tree_widget.cpp
    src/ui/gcode_editor.cpp
    src/ui/camera.cpp
    src/ui/opengl_state.cpp
    src/ui/renderer.cpp
    src/ui/grid_renderer.cpp
    src/ui/axes_renderer.cpp
    src/ui/toolpath_renderer.cpp
    src/ui/position_renderer.cpp
    src/ui/log_settings_dialog.cpp
    src/ui/debug_console_widget.cpp
    src/ui/vulkan_context.cpp
    src/ui/vulkan_buffer.cpp
    src/ui/vulkan_shader.cpp
    src/ui/vulkan_renderer.cpp
    src/utils/logger.cpp
    src/utils/log_config.cpp
    src/utils/result.cpp
    src/engine/interfaces/toolpath_point.cpp
    src/engine/engine_factory.cpp
    src/engine/simulation_engine.cpp
    src/engine/gcode_interpreter.cpp
    src/engine/toolpath_point.cpp
)

# Define header files
set(HEADERS
    include/ui/mainwindow.h
    include/ui/simulation_view.h
    include/ui/project_tree_widget.h
    include/ui/gcode_editor.h
    include/ui/camera.h
    include/ui/opengl_state.h
    include/ui/renderer.h
    include/ui/grid_renderer.h
    include/ui/axes_renderer.h
    include/ui/toolpath_renderer.h
    include/ui/position_renderer.h
    include/ui/log_settings_dialog.h
    include/ui/debug_console_widget.h
    include/ui/vulkan_context.h
    include/ui/vulkan_buffer.h
    include/ui/vulkan_shader.h
    include/ui/vulkan_renderer.h
    include/utils/logger.h
    include/utils/log_config.h
    include/utils/debug_config.h
    include/utils/result.h
    include/engine/interfaces/simulation_engine_interface.h
    include/engine/interfaces/toolpath_point.h
    include/engine/interfaces/engine_factory_interface.h
    include/engine/engine_factory.h
    include/engine/simulation_engine.h
    include/engine/gcode_interpreter.h
    include/engine/toolpath_point.h
)

# Define UI files
set(UI_FILES
    resources/ui/mainwindow.ui
)

# Define resource files
set(RESOURCE_FILES
    resources/resources.qrc
)

# SPIR-V shader compilation function
function(compile_shader target shader_file)
    if(NOT SHADER_COMPILER)
        message(WARNING "No shader compiler found, skipping shader compilation for ${shader_file}")
        return()
    endif()

    get_filename_component(file_name ${shader_file} NAME)
    set(spirv_file "${CMAKE_CURRENT_BINARY_DIR}/shaders/${file_name}.spv")

    if(SHADER_COMPILER_TYPE STREQUAL "glslc")
        add_custom_command(
            OUTPUT ${spirv_file}
            COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_BINARY_DIR}/shaders/"
            COMMAND ${SHADER_COMPILER} ${shader_file} -o ${spirv_file}
            DEPENDS ${shader_file}
            COMMENT "Compiling ${file_name} to SPIR-V using glslc"
            VERBATIM
        )
    elseif(SHADER_COMPILER_TYPE STREQUAL "glslangValidator")
        add_custom_command(
            OUTPUT ${spirv_file}
            COMMAND ${CMAKE_COMMAND} -E make_directory "${CMAKE_CURRENT_BINARY_DIR}/shaders/"
            COMMAND ${SHADER_COMPILER} -V ${shader_file} -o ${spirv_file}
            DEPENDS ${shader_file}
            COMMENT "Compiling ${file_name} to SPIR-V using glslangValidator"
            VERBATIM
        )
    endif()

    target_sources(${target} PRIVATE ${spirv_file})
endfunction()

# Copy shader files to the build directory for direct file access
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/resources/shaders/
     DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/resources/shaders/)

# Copy shader files to the build directory for Qt resource system
file(COPY ${CMAKE_CURRENT_SOURCE_DIR}/resources/shaders/
     DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/resources/)

# Process the shaders.qrc.in file to create shaders.qrc
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/resources/shaders.qrc.in
    ${CMAKE_CURRENT_BINARY_DIR}/resources/shaders.qrc
    @ONLY
)

# Add the generated shaders.qrc to the resource files
list(APPEND RESOURCE_FILES ${CMAKE_CURRENT_BINARY_DIR}/resources/shaders.qrc)

# Create executable
add_executable(Vizion3D
    ${SOURCES}
    ${HEADERS}
    ${UI_FILES}
    ${RESOURCE_FILES}
)

# Link Qt libraries
if(QT_VERSION EQUAL 6)
    target_link_libraries(Vizion3D PRIVATE
        Qt6::Core
        Qt6::Gui
        Qt6::Widgets
        Qt6::OpenGL
        Qt6::OpenGLWidgets
    )
else()
    target_link_libraries(Vizion3D PRIVATE
        Qt5::Core
        Qt5::Gui
        Qt5::Widgets
        Qt5::OpenGL
    )
endif()

# Link Vulkan libraries
target_link_libraries(Vizion3D PRIVATE Vulkan::Vulkan)
if(APPLE AND MOLTENVK_LIBRARY)
    target_link_libraries(Vizion3D PRIVATE ${MOLTENVK_LIBRARY})
endif()

# Link OpenCASCADE if found
if(OpenCASCADE_FOUND)
    target_link_libraries(Vizion3D PRIVATE ${OpenCASCADE_LIBRARIES})
endif()

# Install targets
install(TARGETS Vizion3D
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
)

# Add subdirectories for engine, models, etc.
add_subdirectory(src/engine)
# add_subdirectory(src/models)

# Enable testing
enable_testing()
add_subdirectory(tests)

# Create directories if they don't exist
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/engine)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/engine/interfaces)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/models)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/utils)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src/engine)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src/engine/interfaces)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src/models)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/src/utils)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/resources/shaders)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/resources/styles)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/tests/unit)
file(MAKE_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/tests/integration)

# Print configuration summary
message(STATUS "")
message(STATUS "Vizion3D Configuration Summary")
message(STATUS "-----------------------------")
if(QT_VERSION EQUAL 6)
    message(STATUS "Qt version: ${Qt6_VERSION}")
else()
    message(STATUS "Qt version: ${Qt5_VERSION}")
endif()
message(STATUS "Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "Install prefix: ${CMAKE_INSTALL_PREFIX}")
if(OpenCASCADE_FOUND)
    message(STATUS "OpenCASCADE: Found (${OpenCASCADE_VERSION})")
else()
    message(STATUS "OpenCASCADE: Not found")
endif()
if(Vulkan_FOUND)
    message(STATUS "Vulkan SDK: Found (${Vulkan_VERSION})")
    if(APPLE AND MOLTENVK_LIBRARY)
        message(STATUS "MoltenVK: Found")
    endif()
    if(SHADER_COMPILER)
        message(STATUS "SPIR-V Compiler: Found (${SHADER_COMPILER_TYPE})")
    else()
        message(STATUS "SPIR-V Compiler: Not found")
    endif()
else()
    message(STATUS "Vulkan SDK: Not found")
endif()
message(STATUS "")
message(STATUS "Debug Configuration:")
message(STATUS "  Debug mode: ${VIZION3D_ENABLE_DEBUG}")
message(STATUS "  Logging: ${VIZION3D_ENABLE_LOGGING}")
message(STATUS "  Profiling: ${VIZION3D_ENABLE_PROFILING}")
message(STATUS "  Assertions: ${VIZION3D_ENABLE_ASSERTIONS}")
message(STATUS "-----------------------------")
message(STATUS "")

# --- Clang-Tidy Plugin for Qt Memory Management ---
add_subdirectory(clang-tidy-plugins)
