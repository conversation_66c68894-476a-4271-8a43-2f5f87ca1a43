#include "ui/vulkan_grid_renderer.h"
#include "utils/result.h"
#include <cmath>

namespace Vizion3D::UI {

VulkanGridRenderer::VulkanGridRenderer() = default;

VulkanGridRenderer::~VulkanGridRenderer() = default;

Vizion3D::Utils::Result<void> VulkanGridRenderer::initialize(VulkanContext* context) {
    VIZION3D_PROFILE_FUNCTION();
    
    if (!context) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanContextNotInitialized,
            "VulkanGridRenderer: Context is null"
        );
    }
    
    m_context = context;
    m_device = context->device();
    m_physicalDevice = context->physicalDevice();
    
    // Create uniform buffer
    auto result = createBuffer(
        sizeof(UniformBufferObject),
        VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        m_uniformBuffer
    );
    if (!result.isSuccess()) {
        return result;
    }
    
    // Create descriptor set layout
    result = createDescriptorSetLayout(1, m_descriptorSetLayout);
    if (!result.isSuccess()) {
        return result;
    }
    
    // Create descriptor pool
    result = createDescriptorPool(1, m_descriptorPool);
    if (!result.isSuccess()) {
        return result;
    }
    
    // Allocate descriptor set
    VkDescriptorSetAllocateInfo allocInfo{};
    allocInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_ALLOCATE_INFO;
    allocInfo.descriptorPool = m_descriptorPool;
    allocInfo.descriptorSetCount = 1;
    allocInfo.pSetLayouts = &m_descriptorSetLayout;
    
    VkResult vkResult = vkAllocateDescriptorSets(m_device, &allocInfo, &m_descriptorSet);
    if (vkResult != VK_SUCCESS) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanDescriptorSetLayoutCreationFailed,
            QString("VulkanGridRenderer: Failed to allocate descriptor set: %1").arg(static_cast<int>(vkResult))
        );
    }
    
    // Update descriptor set
    VkDescriptorBufferInfo bufferInfo{};
    bufferInfo.buffer = m_uniformBuffer.buffer();
    bufferInfo.offset = 0;
    bufferInfo.range = sizeof(UniformBufferObject);
    
    VkWriteDescriptorSet descriptorWrite{};
    descriptorWrite.sType = VK_STRUCTURE_TYPE_WRITE_DESCRIPTOR_SET;
    descriptorWrite.dstSet = m_descriptorSet;
    descriptorWrite.dstBinding = 0;
    descriptorWrite.dstArrayElement = 0;
    descriptorWrite.descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
    descriptorWrite.descriptorCount = 1;
    descriptorWrite.pBufferInfo = &bufferInfo;
    
    vkUpdateDescriptorSets(m_device, 1, &descriptorWrite, 0, nullptr);
    
    // Create graphics pipeline
    result = createGraphicsPipeline();
    if (!result.isSuccess()) {
        return result;
    }
    
    // Generate geometry
    generateGridGeometry();
    generateAxesGeometry();
    generateToolpathAreaGeometry();
    
    // Setup buffers
    result = setupGridBuffers();
    if (!result.isSuccess()) {
        return result;
    }
    
    result = setupAxesBuffers();
    if (!result.isSuccess()) {
        return result;
    }
    
    result = setupToolpathAreaBuffers();
    if (!result.isSuccess()) {
        return result;
    }
    
    m_initialized = true;
    m_needsUpdate = false;
    
    VLOG_DEBUG("Vulkan", "VulkanGridRenderer: Initialized successfully");
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<void> VulkanGridRenderer::render(const QMatrix4x4& viewMatrix, 
                                                        const QMatrix4x4& projectionMatrix,
                                                        VkCommandBuffer commandBuffer) {
    VIZION3D_PROFILE_FUNCTION();
    
    if (!m_initialized) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidState,
            "VulkanGridRenderer: Renderer not initialized"
        );
    }
    
    // Update uniform buffer
    auto result = updateUniformBuffer(viewMatrix, projectionMatrix);
    if (!result.isSuccess()) {
        return result;
    }
    
    // Bind pipeline
    vkCmdBindPipeline(commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS, m_pipeline->pipeline());
    
    // Bind descriptor set
    vkCmdBindDescriptorSets(commandBuffer, VK_PIPELINE_BIND_POINT_GRAPHICS, 
                           m_pipeline->pipelineLayout(), 0, 1, &m_descriptorSet, 0, nullptr);
    
    // Draw grid lines
    if (!m_gridVertices.isEmpty()) {
        VkBuffer vertexBuffers[] = {m_gridVertexBuffer.buffer(), m_gridColorBuffer.buffer()};
        VkDeviceSize offsets[] = {0, 0};
        vkCmdBindVertexBuffers(commandBuffer, 0, 2, vertexBuffers, offsets);
        vkCmdDraw(commandBuffer, m_gridVertices.size(), 1, 0, 0);
    }
    
    // Draw main axes
    if (!m_axesVertices.isEmpty()) {
        VkBuffer vertexBuffers[] = {m_axesVertexBuffer.buffer(), m_axesColorBuffer.buffer()};
        VkDeviceSize offsets[] = {0, 0};
        vkCmdBindVertexBuffers(commandBuffer, 0, 2, vertexBuffers, offsets);
        vkCmdDraw(commandBuffer, m_axesVertices.size(), 1, 0, 0);
    }
    
    // Draw toolpath area if enabled
    if (m_highlightToolpathArea && !m_toolpathAreaVertices.isEmpty()) {
        VkBuffer vertexBuffers[] = {m_toolpathAreaVertexBuffer.buffer(), m_toolpathAreaColorBuffer.buffer()};
        VkDeviceSize offsets[] = {0, 0};
        vkCmdBindVertexBuffers(commandBuffer, 0, 2, vertexBuffers, offsets);
        vkCmdDraw(commandBuffer, m_toolpathAreaVertices.size(), 1, 0, 0);
    }
    
    return Vizion3D::Utils::Result<void>::success();
}

void VulkanGridRenderer::setSize(float size) {
    if (m_size != size) {
        m_size = size;
        m_needsUpdate = true;
    }
}

void VulkanGridRenderer::setStep(float step) {
    if (m_step != step) {
        m_step = step;
        m_needsUpdate = true;
    }
}

void VulkanGridRenderer::setColor(const QVector3D& color) {
    if (m_color != color) {
        m_color = color;
        m_needsUpdate = true;
    }
}

void VulkanGridRenderer::setAxisColors(const QVector3D& xColor, const QVector3D& yColor, const QVector3D& zColor) {
    if (m_xAxisColor != xColor || m_yAxisColor != yColor || m_zAxisColor != zColor) {
        m_xAxisColor = xColor;
        m_yAxisColor = yColor;
        m_zAxisColor = zColor;
        m_needsUpdate = true;
    }
}

void VulkanGridRenderer::setHighlightToolpathArea(bool highlight) {
    m_highlightToolpathArea = highlight;
}

void VulkanGridRenderer::setToolpathAreaBounds(const QVector3D& min, const QVector3D& max) {
    if (m_toolpathAreaMin != min || m_toolpathAreaMax != max) {
        m_toolpathAreaMin = min;
        m_toolpathAreaMax = max;
        m_needsUpdate = true;
    }
}

void VulkanGridRenderer::generateGridGeometry() {
    VIZION3D_PROFILE_FUNCTION();

    m_gridVertices.clear();
    m_gridColors.clear();

    float halfSize = m_size / 2.0f;

    // Generate grid lines parallel to X axis
    for (float z = -halfSize; z <= halfSize; z += m_step) {
        m_gridVertices.append(QVector3D(-halfSize, 0.0f, z));
        m_gridVertices.append(QVector3D(halfSize, 0.0f, z));
        m_gridColors.append(m_color);
        m_gridColors.append(m_color);
    }

    // Generate grid lines parallel to Z axis
    for (float x = -halfSize; x <= halfSize; x += m_step) {
        m_gridVertices.append(QVector3D(x, 0.0f, -halfSize));
        m_gridVertices.append(QVector3D(x, 0.0f, halfSize));
        m_gridColors.append(m_color);
        m_gridColors.append(m_color);
    }

    VLOG_DEBUG("Vulkan", QString("VulkanGridRenderer: Generated %1 grid vertices").arg(m_gridVertices.size()));
}

void VulkanGridRenderer::generateAxesGeometry() {
    VIZION3D_PROFILE_FUNCTION();

    m_axesVertices.clear();
    m_axesColors.clear();

    float axisLength = m_size / 2.0f;

    // X axis (red)
    m_axesVertices.append(QVector3D(0.0f, 0.0f, 0.0f));
    m_axesVertices.append(QVector3D(axisLength, 0.0f, 0.0f));
    m_axesColors.append(m_xAxisColor);
    m_axesColors.append(m_xAxisColor);

    // Y axis (green)
    m_axesVertices.append(QVector3D(0.0f, 0.0f, 0.0f));
    m_axesVertices.append(QVector3D(0.0f, axisLength, 0.0f));
    m_axesColors.append(m_yAxisColor);
    m_axesColors.append(m_yAxisColor);

    // Z axis (blue)
    m_axesVertices.append(QVector3D(0.0f, 0.0f, 0.0f));
    m_axesVertices.append(QVector3D(0.0f, 0.0f, axisLength));
    m_axesColors.append(m_zAxisColor);
    m_axesColors.append(m_zAxisColor);

    VLOG_DEBUG("Vulkan", QString("VulkanGridRenderer: Generated %1 axes vertices").arg(m_axesVertices.size()));
}

void VulkanGridRenderer::generateToolpathAreaGeometry() {
    VIZION3D_PROFILE_FUNCTION();

    m_toolpathAreaVertices.clear();
    m_toolpathAreaColors.clear();

    if (!m_highlightToolpathArea) {
        return;
    }

    QVector3D highlightColor(1.0f, 1.0f, 0.0f); // Yellow highlight

    // Create a rectangle outline for the toolpath area
    float minX = m_toolpathAreaMin.x();
    float minZ = m_toolpathAreaMin.z();
    float maxX = m_toolpathAreaMax.x();
    float maxZ = m_toolpathAreaMax.z();
    float y = 0.01f; // Slightly above the grid

    // Bottom edge
    m_toolpathAreaVertices.append(QVector3D(minX, y, minZ));
    m_toolpathAreaVertices.append(QVector3D(maxX, y, minZ));
    m_toolpathAreaColors.append(highlightColor);
    m_toolpathAreaColors.append(highlightColor);

    // Right edge
    m_toolpathAreaVertices.append(QVector3D(maxX, y, minZ));
    m_toolpathAreaVertices.append(QVector3D(maxX, y, maxZ));
    m_toolpathAreaColors.append(highlightColor);
    m_toolpathAreaColors.append(highlightColor);

    // Top edge
    m_toolpathAreaVertices.append(QVector3D(maxX, y, maxZ));
    m_toolpathAreaVertices.append(QVector3D(minX, y, maxZ));
    m_toolpathAreaColors.append(highlightColor);
    m_toolpathAreaColors.append(highlightColor);

    // Left edge
    m_toolpathAreaVertices.append(QVector3D(minX, y, maxZ));
    m_toolpathAreaVertices.append(QVector3D(minX, y, minZ));
    m_toolpathAreaColors.append(highlightColor);
    m_toolpathAreaColors.append(highlightColor);

    VLOG_DEBUG("Vulkan", QString("VulkanGridRenderer: Generated %1 toolpath area vertices").arg(m_toolpathAreaVertices.size()));
}

Vizion3D::Utils::Result<void> VulkanGridRenderer::createGraphicsPipeline() {
    VIZION3D_PROFILE_FUNCTION();

    // Load shaders
    auto vertexShaderResult = VulkanShader::loadShaderFromFile(":/shaders/grid.vert.spv");
    if (!vertexShaderResult.isSuccess()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanShaderModuleCreationFailed,
            QString("VulkanGridRenderer: Failed to load vertex shader: %1").arg(vertexShaderResult.error().message)
        );
    }

    auto fragmentShaderResult = VulkanShader::loadShaderFromFile(":/shaders/grid.frag.spv");
    if (!fragmentShaderResult.isSuccess()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanShaderModuleCreationFailed,
            QString("VulkanGridRenderer: Failed to load fragment shader: %1").arg(fragmentShaderResult.error().message)
        );
    }

    // Define vertex input attributes
    std::vector<VulkanShader::VertexInputBinding> bindings = {
        {0, sizeof(QVector3D), VK_VERTEX_INPUT_RATE_VERTEX}, // Position
        {1, sizeof(QVector3D), VK_VERTEX_INPUT_RATE_VERTEX}  // Color
    };

    std::vector<VulkanShader::VertexInputAttribute> attributes = {
        {0, 0, VK_FORMAT_R32G32B32_SFLOAT, 0}, // Position
        {1, 1, VK_FORMAT_R32G32B32_SFLOAT, 0}  // Color
    };

    // Create pipeline
    VulkanShader::PipelineCreateInfo createInfo{};
    createInfo.vertexShaderSpirv = vertexShaderResult.value();
    createInfo.fragmentShaderSpirv = fragmentShaderResult.value();
    createInfo.vertexBindings = bindings;
    createInfo.vertexAttributes = attributes;
    createInfo.topology = VK_PRIMITIVE_TOPOLOGY_LINE_LIST;
    createInfo.descriptorSetLayout = m_descriptorSetLayout;

    return createGraphicsPipeline(createInfo, m_pipeline);
}

Vizion3D::Utils::Result<void> VulkanGridRenderer::setupGridBuffers() {
    VIZION3D_PROFILE_FUNCTION();

    if (m_gridVertices.isEmpty()) {
        return Vizion3D::Utils::Result<void>::success();
    }

    // Create vertex buffer
    VkDeviceSize vertexBufferSize = m_gridVertices.size() * sizeof(QVector3D);
    auto result = createBuffer(
        vertexBufferSize,
        VK_BUFFER_USAGE_VERTEX_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        m_gridVertexBuffer
    );
    if (!result.isSuccess()) {
        return result;
    }

    // Upload vertex data
    result = m_gridVertexBuffer.updateData(m_gridVertices.constData(), vertexBufferSize);
    if (!result.isSuccess()) {
        return result;
    }

    // Create color buffer
    VkDeviceSize colorBufferSize = m_gridColors.size() * sizeof(QVector3D);
    result = createBuffer(
        colorBufferSize,
        VK_BUFFER_USAGE_VERTEX_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        m_gridColorBuffer
    );
    if (!result.isSuccess()) {
        return result;
    }

    // Upload color data
    result = m_gridColorBuffer.updateData(m_gridColors.constData(), colorBufferSize);
    if (!result.isSuccess()) {
        return result;
    }

    VLOG_DEBUG("Vulkan", QString("VulkanGridRenderer: Set up grid buffers with %1 vertices").arg(m_gridVertices.size()));
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<void> VulkanGridRenderer::setupAxesBuffers() {
    VIZION3D_PROFILE_FUNCTION();

    if (m_axesVertices.isEmpty()) {
        return Vizion3D::Utils::Result<void>::success();
    }

    // Create vertex buffer
    VkDeviceSize vertexBufferSize = m_axesVertices.size() * sizeof(QVector3D);
    auto result = createBuffer(
        vertexBufferSize,
        VK_BUFFER_USAGE_VERTEX_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        m_axesVertexBuffer
    );
    if (!result.isSuccess()) {
        return result;
    }

    // Upload vertex data
    result = m_axesVertexBuffer.updateData(m_axesVertices.constData(), vertexBufferSize);
    if (!result.isSuccess()) {
        return result;
    }

    // Create color buffer
    VkDeviceSize colorBufferSize = m_axesColors.size() * sizeof(QVector3D);
    result = createBuffer(
        colorBufferSize,
        VK_BUFFER_USAGE_VERTEX_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        m_axesColorBuffer
    );
    if (!result.isSuccess()) {
        return result;
    }

    // Upload color data
    result = m_axesColorBuffer.updateData(m_axesColors.constData(), colorBufferSize);
    if (!result.isSuccess()) {
        return result;
    }

    VLOG_DEBUG("Vulkan", QString("VulkanGridRenderer: Set up axes buffers with %1 vertices").arg(m_axesVertices.size()));
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<void> VulkanGridRenderer::setupToolpathAreaBuffers() {
    VIZION3D_PROFILE_FUNCTION();

    if (m_toolpathAreaVertices.isEmpty()) {
        return Vizion3D::Utils::Result<void>::success();
    }

    // Create vertex buffer
    VkDeviceSize vertexBufferSize = m_toolpathAreaVertices.size() * sizeof(QVector3D);
    auto result = createBuffer(
        vertexBufferSize,
        VK_BUFFER_USAGE_VERTEX_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        m_toolpathAreaVertexBuffer
    );
    if (!result.isSuccess()) {
        return result;
    }

    // Upload vertex data
    result = m_toolpathAreaVertexBuffer.updateData(m_toolpathAreaVertices.constData(), vertexBufferSize);
    if (!result.isSuccess()) {
        return result;
    }

    // Create color buffer
    VkDeviceSize colorBufferSize = m_toolpathAreaColors.size() * sizeof(QVector3D);
    result = createBuffer(
        colorBufferSize,
        VK_BUFFER_USAGE_VERTEX_BUFFER_BIT,
        VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT,
        m_toolpathAreaColorBuffer
    );
    if (!result.isSuccess()) {
        return result;
    }

    // Upload color data
    result = m_toolpathAreaColorBuffer.updateData(m_toolpathAreaColors.constData(), colorBufferSize);
    if (!result.isSuccess()) {
        return result;
    }

    VLOG_DEBUG("Vulkan", QString("VulkanGridRenderer: Set up toolpath area buffers with %1 vertices").arg(m_toolpathAreaVertices.size()));
    return Vizion3D::Utils::Result<void>::success();
}

} // namespace Vizion3D::UI
