#include "ui/vulkan_buffer.h"
#include "ui/vulkan_context.h"
#include <cstring>

namespace Vizion3D::UI {

VulkanBuffer::VulkanBuffer() = default;

VulkanBuffer::~VulkanBuffer() {
    cleanup();
}

VulkanBuffer::VulkanBuffer(VulkanBuffer&& other) noexcept
    : m_device(other.m_device)
    , m_buffer(other.m_buffer)
    , m_memory(other.m_memory)
    , m_size(other.m_size)
    , m_usage(other.m_usage)
    , m_memoryProperties(other.m_memoryProperties)
    , m_mappedMemory(other.m_mappedMemory)
    , m_context(other.m_context) {
    
    // Reset other object
    other.m_device = VK_NULL_HANDLE;
    other.m_buffer = VK_NULL_HANDLE;
    other.m_memory = VK_NULL_HANDLE;
    other.m_size = 0;
    other.m_usage = 0;
    other.m_memoryProperties = 0;
    other.m_mappedMemory = nullptr;
    other.m_context = nullptr;
}

VulkanBuffer& VulkanBuffer::operator=(VulkanBuffer&& other) noexcept {
    if (this != &other) {
        cleanup();
        
        m_device = other.m_device;
        m_buffer = other.m_buffer;
        m_memory = other.m_memory;
        m_size = other.m_size;
        m_usage = other.m_usage;
        m_memoryProperties = other.m_memoryProperties;
        m_mappedMemory = other.m_mappedMemory;
        m_context = other.m_context;
        
        // Reset other object
        other.m_device = VK_NULL_HANDLE;
        other.m_buffer = VK_NULL_HANDLE;
        other.m_memory = VK_NULL_HANDLE;
        other.m_size = 0;
        other.m_usage = 0;
        other.m_memoryProperties = 0;
        other.m_mappedMemory = nullptr;
        other.m_context = nullptr;
    }
    return *this;
}

Vizion3D::Utils::Result<void> VulkanBuffer::create(const VulkanContext* context,
                                                   VkDeviceSize size,
                                                   VkBufferUsageFlags usage,
                                                   VkMemoryPropertyFlags properties) {
    VIZION3D_PROFILE_FUNCTION();
    
    if (!context) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidArgument,
            "VulkanContext cannot be null"
        );
    }
    
    if (size == 0) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidArgument,
            "Buffer size cannot be zero"
        );
    }
    
    // Clean up any existing resources
    cleanup();
    
    m_context = context;
    m_device = context->device();
    m_size = size;
    m_usage = usage;
    m_memoryProperties = properties;
    
    // Create buffer
    VkBufferCreateInfo bufferInfo{};
    bufferInfo.sType = VK_STRUCTURE_TYPE_BUFFER_CREATE_INFO;
    bufferInfo.size = size;
    bufferInfo.usage = usage;
    bufferInfo.sharingMode = VK_SHARING_MODE_EXCLUSIVE;
    
    VkResult result = vkCreateBuffer(m_device, &bufferInfo, nullptr, &m_buffer);
    if (result != VK_SUCCESS) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanBufferCreationFailed,
            QString("Failed to create buffer: %1").arg(result)
        );
    }
    
    // Get memory requirements
    VkMemoryRequirements memRequirements;
    vkGetBufferMemoryRequirements(m_device, m_buffer, &memRequirements);
    
    // Find suitable memory type
    auto memoryTypeResult = context->findMemoryType(memRequirements.memoryTypeBits, properties);
    if (memoryTypeResult.isError()) {
        cleanup();
        return Vizion3D::Utils::Result<void>::error(memoryTypeResult.error());
    }
    
    // Allocate memory
    VkMemoryAllocateInfo allocInfo{};
    allocInfo.sType = VK_STRUCTURE_TYPE_MEMORY_ALLOCATE_INFO;
    allocInfo.allocationSize = memRequirements.size;
    allocInfo.memoryTypeIndex = memoryTypeResult.value();
    
    result = vkAllocateMemory(m_device, &allocInfo, nullptr, &m_memory);
    if (result != VK_SUCCESS) {
        cleanup();
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanMemoryAllocationFailed,
            QString("Failed to allocate buffer memory: %1").arg(result)
        );
    }
    
    // Bind buffer to memory
    result = vkBindBufferMemory(m_device, m_buffer, m_memory, 0);
    if (result != VK_SUCCESS) {
        cleanup();
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanBufferCreationFailed,
            QString("Failed to bind buffer memory: %1").arg(result)
        );
    }
    
    VLOG_DEBUG("Vulkan", QString("Created buffer of size %1 bytes").arg(size));
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<void> VulkanBuffer::updateData(const void* data, VkDeviceSize size, VkDeviceSize offset) {
    VIZION3D_PROFILE_FUNCTION();
    
    if (!isValid()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidState,
            "Buffer is not valid"
        );
    }
    
    if (!data) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidArgument,
            "Data pointer cannot be null"
        );
    }
    
    if (offset + size > m_size) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidRange,
            QString("Data size (%1) + offset (%2) exceeds buffer size (%3)")
                .arg(size).arg(offset).arg(m_size)
        );
    }
    
    // Check if memory is host visible
    if (!(m_memoryProperties & VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT)) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::InvalidOperation,
            "Buffer memory is not host visible - use staging buffer for updates"
        );
    }
    
    auto mapResult = map(offset, size);
    if (mapResult.isError()) {
        return Vizion3D::Utils::Result<void>::error(mapResult.error());
    }
    
    std::memcpy(mapResult.value(), data, size);
    
    // Flush memory if not coherent
    if (!(m_memoryProperties & VK_MEMORY_PROPERTY_HOST_COHERENT_BIT)) {
        VkMappedMemoryRange mappedRange{};
        mappedRange.sType = VK_STRUCTURE_TYPE_MAPPED_MEMORY_RANGE;
        mappedRange.memory = m_memory;
        mappedRange.offset = offset;
        mappedRange.size = size;
        
        VkResult result = vkFlushMappedMemoryRanges(m_device, 1, &mappedRange);
        if (result != VK_SUCCESS) {
            unmap();
            return Vizion3D::Utils::Result<void>::error(
                Vizion3D::Utils::ErrorCode::VulkanMemoryAllocationFailed,
                QString("Failed to flush mapped memory: %1").arg(result)
            );
        }
    }
    
    unmap();
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<void*> VulkanBuffer::map(VkDeviceSize offset, VkDeviceSize size) {
    if (!isValid()) {
        return Vizion3D::Utils::Result<void*>::error(
            Vizion3D::Utils::ErrorCode::InvalidState,
            "Buffer is not valid"
        );
    }
    
    if (m_mappedMemory) {
        return Vizion3D::Utils::Result<void*>::error(
            Vizion3D::Utils::ErrorCode::InvalidState,
            "Buffer is already mapped"
        );
    }
    
    VkResult result = vkMapMemory(m_device, m_memory, offset, size, 0, &m_mappedMemory);
    if (result != VK_SUCCESS) {
        return Vizion3D::Utils::Result<void*>::error(
            Vizion3D::Utils::ErrorCode::VulkanMemoryAllocationFailed,
            QString("Failed to map buffer memory: %1").arg(result)
        );
    }
    
    return Vizion3D::Utils::Result<void*>::success(m_mappedMemory);
}

void VulkanBuffer::unmap() {
    if (m_mappedMemory && m_device != VK_NULL_HANDLE) {
        vkUnmapMemory(m_device, m_memory);
        m_mappedMemory = nullptr;
    }
}

VkDescriptorBufferInfo VulkanBuffer::getDescriptorInfo(VkDeviceSize offset, VkDeviceSize range) const {
    VkDescriptorBufferInfo bufferInfo{};
    bufferInfo.buffer = m_buffer;
    bufferInfo.offset = offset;
    bufferInfo.range = (range == VK_WHOLE_SIZE) ? m_size : range;
    return bufferInfo;
}

void VulkanBuffer::cleanup() {
    unmap();
    
    if (m_buffer != VK_NULL_HANDLE && m_device != VK_NULL_HANDLE) {
        vkDestroyBuffer(m_device, m_buffer, nullptr);
        m_buffer = VK_NULL_HANDLE;
    }
    
    if (m_memory != VK_NULL_HANDLE && m_device != VK_NULL_HANDLE) {
        vkFreeMemory(m_device, m_memory, nullptr);
        m_memory = VK_NULL_HANDLE;
    }
    
    m_device = VK_NULL_HANDLE;
    m_size = 0;
    m_usage = 0;
    m_memoryProperties = 0;
    m_context = nullptr;
}

// Helper functions
Vizion3D::Utils::Result<VulkanBuffer> createStagingBuffer(const VulkanContext* context, VkDeviceSize size) {
    VulkanBuffer buffer;
    auto result = buffer.create(context, size,
                               VK_BUFFER_USAGE_TRANSFER_SRC_BIT,
                               VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT);

    if (result.isError()) {
        return Vizion3D::Utils::Result<VulkanBuffer>::error(result.error());
    }

    return Vizion3D::Utils::Result<VulkanBuffer>::success(std::move(buffer));
}

Vizion3D::Utils::Result<VulkanBuffer> createVertexBuffer(const VulkanContext* context, VkDeviceSize size) {
    VulkanBuffer buffer;
    auto result = buffer.create(context, size,
                               VK_BUFFER_USAGE_TRANSFER_DST_BIT | VK_BUFFER_USAGE_VERTEX_BUFFER_BIT,
                               VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT);

    if (result.isError()) {
        return Vizion3D::Utils::Result<VulkanBuffer>::error(result.error());
    }

    return Vizion3D::Utils::Result<VulkanBuffer>::success(std::move(buffer));
}

Vizion3D::Utils::Result<VulkanBuffer> createIndexBuffer(const VulkanContext* context, VkDeviceSize size) {
    VulkanBuffer buffer;
    auto result = buffer.create(context, size,
                               VK_BUFFER_USAGE_TRANSFER_DST_BIT | VK_BUFFER_USAGE_INDEX_BUFFER_BIT,
                               VK_MEMORY_PROPERTY_DEVICE_LOCAL_BIT);

    if (result.isError()) {
        return Vizion3D::Utils::Result<VulkanBuffer>::error(result.error());
    }

    return Vizion3D::Utils::Result<VulkanBuffer>::success(std::move(buffer));
}

Vizion3D::Utils::Result<VulkanBuffer> createUniformBuffer(const VulkanContext* context, VkDeviceSize size) {
    VulkanBuffer buffer;
    auto result = buffer.create(context, size,
                               VK_BUFFER_USAGE_UNIFORM_BUFFER_BIT,
                               VK_MEMORY_PROPERTY_HOST_VISIBLE_BIT | VK_MEMORY_PROPERTY_HOST_COHERENT_BIT);

    if (result.isError()) {
        return Vizion3D::Utils::Result<VulkanBuffer>::error(result.error());
    }

    return Vizion3D::Utils::Result<VulkanBuffer>::success(std::move(buffer));
}

} // namespace Vizion3D::UI
