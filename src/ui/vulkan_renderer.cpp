#include "ui/vulkan_renderer.h"
#include "utils/result.h"
#include <cstring>

namespace Vizion3D::UI {

VulkanRenderer::VulkanRenderer() = default;

VulkanRenderer::~VulkanRenderer() {
    cleanup();
}

VulkanRenderer::Vulkan<PERSON>enderer(VulkanRenderer&& other) noexcept
    : m_context(other.m_context)
    , m_device(other.m_device)
    , m_physicalDevice(other.m_physicalDevice)
    , m_uniformBuffer(std::move(other.m_uniformBuffer))
    , m_descriptorSetLayout(other.m_descriptorSetLayout)
    , m_descriptorPool(other.m_descriptorPool)
    , m_descriptorSet(other.m_descriptorSet)
    , m_initialized(other.m_initialized)
{
    // Reset moved-from object
    other.m_context = nullptr;
    other.m_device = VK_NULL_HANDLE;
    other.m_physicalDevice = VK_NULL_HANDLE;
    other.m_descriptorSetLayout = VK_NULL_HANDLE;
    other.m_descriptorPool = VK_NULL_HANDLE;
    other.m_descriptorSet = VK_NULL_HANDLE;
    other.m_initialized = false;
}

VulkanRenderer& VulkanRenderer::operator=(VulkanRenderer&& other) noexcept {
    if (this != &other) {
        cleanup();
        
        m_context = other.m_context;
        m_device = other.m_device;
        m_physicalDevice = other.m_physicalDevice;
        m_uniformBuffer = std::move(other.m_uniformBuffer);
        m_descriptorSetLayout = other.m_descriptorSetLayout;
        m_descriptorPool = other.m_descriptorPool;
        m_descriptorSet = other.m_descriptorSet;
        m_initialized = other.m_initialized;
        
        // Reset moved-from object
        other.m_context = nullptr;
        other.m_device = VK_NULL_HANDLE;
        other.m_physicalDevice = VK_NULL_HANDLE;
        other.m_descriptorSetLayout = VK_NULL_HANDLE;
        other.m_descriptorPool = VK_NULL_HANDLE;
        other.m_descriptorSet = VK_NULL_HANDLE;
        other.m_initialized = false;
    }
    return *this;
}

Vizion3D::Utils::Result<void> VulkanRenderer::createBuffer(VkDeviceSize size, 
                                                           VkBufferUsageFlags usage, 
                                                           VkMemoryPropertyFlags properties, 
                                                           VulkanBuffer& buffer) {
    VIZION3D_PROFILE_FUNCTION();
    
    if (!m_context) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanContextNotInitialized,
            "VulkanRenderer: Context not initialized when creating buffer"
        );
    }
    
    auto result = buffer.create(m_context, size, usage, properties);
    if (!result.isSuccess()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanBufferCreationFailed,
            QString("VulkanRenderer: Failed to create buffer: %1").arg(result.error().message)
        );
    }
    
    VLOG_DEBUG("Vulkan", QString("VulkanRenderer: Created buffer of size %1 bytes").arg(size));
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<void> VulkanRenderer::createGraphicsPipeline(
    const VulkanShader::PipelineCreateInfo& createInfo,
    std::unique_ptr<VulkanShader>& pipeline) {
    
    VIZION3D_PROFILE_FUNCTION();
    
    if (!m_context) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanContextNotInitialized,
            "VulkanRenderer: Context not initialized when creating graphics pipeline"
        );
    }
    
    pipeline = std::make_unique<VulkanShader>();
    auto result = pipeline->createGraphicsPipeline(m_context, createInfo);
    if (!result.isSuccess()) {
        pipeline.reset();
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanPipelineCreationFailed,
            QString("VulkanRenderer: Failed to create graphics pipeline: %1").arg(result.error().message)
        );
    }
    
    VLOG_DEBUG("Vulkan", "VulkanRenderer: Created graphics pipeline successfully");
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<void> VulkanRenderer::createDescriptorSetLayout(uint32_t bindingCount,
                                                                        VkDescriptorSetLayout& descriptorSetLayout) {
    VIZION3D_PROFILE_FUNCTION();
    
    if (!m_context) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanContextNotInitialized,
            "VulkanRenderer: Context not initialized when creating descriptor set layout"
        );
    }
    
    VkDescriptorSetLayoutBinding uboLayoutBinding{};
    uboLayoutBinding.binding = 0;
    uboLayoutBinding.descriptorType = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
    uboLayoutBinding.descriptorCount = 1;
    uboLayoutBinding.stageFlags = VK_SHADER_STAGE_VERTEX_BIT | VK_SHADER_STAGE_FRAGMENT_BIT;
    uboLayoutBinding.pImmutableSamplers = nullptr;
    
    VkDescriptorSetLayoutCreateInfo layoutInfo{};
    layoutInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_SET_LAYOUT_CREATE_INFO;
    layoutInfo.bindingCount = 1;
    layoutInfo.pBindings = &uboLayoutBinding;
    
    VkResult result = vkCreateDescriptorSetLayout(m_device, &layoutInfo, nullptr, &descriptorSetLayout);
    if (result != VK_SUCCESS) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanDescriptorSetLayoutCreationFailed,
            QString("VulkanRenderer: Failed to create descriptor set layout: %1").arg(static_cast<int>(result))
        );
    }
    
    VLOG_DEBUG("Vulkan", "VulkanRenderer: Created descriptor set layout successfully");
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<void> VulkanRenderer::createDescriptorPool(uint32_t maxSets,
                                                                   VkDescriptorPool& descriptorPool) {
    VIZION3D_PROFILE_FUNCTION();
    
    if (!m_context) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanContextNotInitialized,
            "VulkanRenderer: Context not initialized when creating descriptor pool"
        );
    }
    
    VkDescriptorPoolSize poolSize{};
    poolSize.type = VK_DESCRIPTOR_TYPE_UNIFORM_BUFFER;
    poolSize.descriptorCount = maxSets;
    
    VkDescriptorPoolCreateInfo poolInfo{};
    poolInfo.sType = VK_STRUCTURE_TYPE_DESCRIPTOR_POOL_CREATE_INFO;
    poolInfo.poolSizeCount = 1;
    poolInfo.pPoolSizes = &poolSize;
    poolInfo.maxSets = maxSets;
    
    VkResult result = vkCreateDescriptorPool(m_device, &poolInfo, nullptr, &descriptorPool);
    if (result != VK_SUCCESS) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanDescriptorPoolCreationFailed,
            QString("VulkanRenderer: Failed to create descriptor pool: %1").arg(static_cast<int>(result))
        );
    }
    
    VLOG_DEBUG("Vulkan", "VulkanRenderer: Created descriptor pool successfully");
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<void> VulkanRenderer::updateUniformBuffer(const QMatrix4x4& viewMatrix,
                                                                  const QMatrix4x4& projectionMatrix,
                                                                  const QVector4D& color) {
    VIZION3D_PROFILE_FUNCTION();
    
    if (!m_uniformBuffer.isValid()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanBufferNotInitialized,
            "VulkanRenderer: Uniform buffer not initialized"
        );
    }
    
    UniformBufferObject ubo{};
    QMatrix4x4 modelMatrix;
    modelMatrix.setToIdentity();
    
    ubo.modelViewProjection = projectionMatrix * viewMatrix * modelMatrix;
    ubo.modelView = viewMatrix * modelMatrix;
    ubo.color = color;
    
    auto result = m_uniformBuffer.updateData(&ubo, sizeof(ubo));
    if (!result.isSuccess()) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanBufferUpdateFailed,
            QString("VulkanRenderer: Failed to update uniform buffer: %1").arg(result.error().message)
        );
    }
    
    return Vizion3D::Utils::Result<void>::success();
}

void VulkanRenderer::cleanup() {
    if (m_device != VK_NULL_HANDLE) {
        if (m_descriptorPool != VK_NULL_HANDLE) {
            vkDestroyDescriptorPool(m_device, m_descriptorPool, nullptr);
            m_descriptorPool = VK_NULL_HANDLE;
        }
        
        if (m_descriptorSetLayout != VK_NULL_HANDLE) {
            vkDestroyDescriptorSetLayout(m_device, m_descriptorSetLayout, nullptr);
            m_descriptorSetLayout = VK_NULL_HANDLE;
        }
    }
    
    m_descriptorSet = VK_NULL_HANDLE;
    m_initialized = false;
}

} // namespace Vizion3D::UI
