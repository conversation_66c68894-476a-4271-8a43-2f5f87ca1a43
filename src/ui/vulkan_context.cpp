#include "ui/vulkan_context.h"
#include "utils/debug_config.h"
#include <QDebug>
#include <set>
#include <algorithm>

namespace Vizion3D::UI {

// Static member definitions
const QStringList VulkanContext::s_validationLayers = {
    "VK_LAYER_KHRONOS_validation"
};

const QStringList VulkanContext::s_deviceExtensions = {
    VK_KHR_SWAPCHAIN_EXTENSION_NAME
};

// Debug callback function
static VKAPI_ATTR VkBool32 VKAPI_CALL debugCallback(
    VkDebugUtilsMessageSeverityFlagBitsEXT messageSeverity,
    VkDebugUtilsMessageTypeFlagsEXT messageType,
    const VkDebugUtilsMessengerCallbackDataEXT* pCallbackData,
    void* pUserData) {
    
    QString message = QString("Vulkan Validation: %1").arg(pCallbackData->pMessage);
    
    switch (messageSeverity) {
        case VK_DEBUG_UTILS_MESSAGE_SEVERITY_VERBOSE_BIT_EXT:
            VLOG_TRACE("Vulkan", message);
            break;
        case VK_DEBUG_UTILS_MESSAGE_SEVERITY_INFO_BIT_EXT:
            VLOG_DEBUG("Vulkan", message);
            break;
        case VK_DEBUG_UTILS_MESSAGE_SEVERITY_WARNING_BIT_EXT:
            VLOG_WARNING("Vulkan", message);
            break;
        case VK_DEBUG_UTILS_MESSAGE_SEVERITY_ERROR_BIT_EXT:
            VLOG_ERROR("Vulkan", message);
            break;
        default:
            VLOG_DEBUG("Vulkan", message);
            break;
    }
    
    return VK_FALSE;
}

Vizion3D::Utils::Result<std::unique_ptr<VulkanContext>> VulkanContext::create(bool enableValidation) {
    VIZION3D_PROFILE_FUNCTION();
    
    auto context = std::unique_ptr<VulkanContext>(new VulkanContext());
    auto result = context->initialize(enableValidation);
    
    if (result.isError()) {
        return Vizion3D::Utils::Result<std::unique_ptr<VulkanContext>>::error(result.error());
    }
    
    VLOG_INFO("Vulkan", "VulkanContext created successfully");
    return Vizion3D::Utils::Result<std::unique_ptr<VulkanContext>>::success(std::move(context));
}

VulkanContext::~VulkanContext() {
    cleanup();
}

VulkanContext::VulkanContext(VulkanContext&& other) noexcept
    : m_instance(other.m_instance)
    , m_device(other.m_device)
    , m_physicalDevice(other.m_physicalDevice)
    , m_graphicsQueue(other.m_graphicsQueue)
    , m_presentQueue(other.m_presentQueue)
    , m_graphicsQueueFamily(other.m_graphicsQueueFamily)
    , m_presentQueueFamily(other.m_presentQueueFamily)
    , m_deviceProperties(other.m_deviceProperties)
    , m_deviceFeatures(other.m_deviceFeatures)
    , m_memoryProperties(other.m_memoryProperties)
    , m_debugMessenger(other.m_debugMessenger)
    , m_validationEnabled(other.m_validationEnabled) {
    
    // Reset other object
    other.m_instance = VK_NULL_HANDLE;
    other.m_device = VK_NULL_HANDLE;
    other.m_physicalDevice = VK_NULL_HANDLE;
    other.m_graphicsQueue = VK_NULL_HANDLE;
    other.m_presentQueue = VK_NULL_HANDLE;
    other.m_debugMessenger = VK_NULL_HANDLE;
}

VulkanContext& VulkanContext::operator=(VulkanContext&& other) noexcept {
    if (this != &other) {
        cleanup();
        
        m_instance = other.m_instance;
        m_device = other.m_device;
        m_physicalDevice = other.m_physicalDevice;
        m_graphicsQueue = other.m_graphicsQueue;
        m_presentQueue = other.m_presentQueue;
        m_graphicsQueueFamily = other.m_graphicsQueueFamily;
        m_presentQueueFamily = other.m_presentQueueFamily;
        m_deviceProperties = other.m_deviceProperties;
        m_deviceFeatures = other.m_deviceFeatures;
        m_memoryProperties = other.m_memoryProperties;
        m_debugMessenger = other.m_debugMessenger;
        m_validationEnabled = other.m_validationEnabled;
        
        // Reset other object
        other.m_instance = VK_NULL_HANDLE;
        other.m_device = VK_NULL_HANDLE;
        other.m_physicalDevice = VK_NULL_HANDLE;
        other.m_graphicsQueue = VK_NULL_HANDLE;
        other.m_presentQueue = VK_NULL_HANDLE;
        other.m_debugMessenger = VK_NULL_HANDLE;
    }
    return *this;
}

Vizion3D::Utils::Result<uint32_t> VulkanContext::findMemoryType(uint32_t typeFilter, 
                                                                VkMemoryPropertyFlags properties) const {
    for (uint32_t i = 0; i < m_memoryProperties.memoryTypeCount; i++) {
        if ((typeFilter & (1 << i)) && 
            (m_memoryProperties.memoryTypes[i].propertyFlags & properties) == properties) {
            return Vizion3D::Utils::Result<uint32_t>::success(i);
        }
    }
    
    return Vizion3D::Utils::Result<uint32_t>::error(
        Vizion3D::Utils::ErrorCode::VulkanMemoryAllocationFailed,
        "Failed to find suitable memory type"
    );
}

Vizion3D::Utils::Result<void> VulkanContext::initialize(bool enableValidation) {
    VIZION3D_PROFILE_FUNCTION();
    
    m_validationEnabled = enableValidation;
    
    // Check validation layer support if requested
    if (m_validationEnabled && !checkValidationLayerSupport()) {
        VLOG_WARNING("Vulkan", "Validation layers requested but not available, disabling validation");
        m_validationEnabled = false;
    }
    
    auto result = createInstance();
    if (result.isError()) return result;
    
    if (m_validationEnabled) {
        result = setupDebugMessenger();
        if (result.isError()) return result;
    }
    
    result = selectPhysicalDevice();
    if (result.isError()) return result;
    
    result = createLogicalDevice();
    if (result.isError()) return result;
    
    VLOG_INFO("Vulkan", QString("Vulkan context initialized successfully with device: %1")
              .arg(m_deviceProperties.deviceName));
    
    return Vizion3D::Utils::Result<void>::success();
}

Vizion3D::Utils::Result<void> VulkanContext::createInstance() {
    VIZION3D_PROFILE_FUNCTION();
    
    VkApplicationInfo appInfo{};
    appInfo.sType = VK_STRUCTURE_TYPE_APPLICATION_INFO;
    appInfo.pApplicationName = "Vizion3D";
    appInfo.applicationVersion = VK_MAKE_VERSION(1, 0, 0);
    appInfo.pEngineName = "Vizion3D Engine";
    appInfo.engineVersion = VK_MAKE_VERSION(1, 0, 0);
    appInfo.apiVersion = VK_API_VERSION_1_3;
    
    VkInstanceCreateInfo createInfo{};
    createInfo.sType = VK_STRUCTURE_TYPE_INSTANCE_CREATE_INFO;
    createInfo.pApplicationInfo = &appInfo;
    
    // Get required extensions
    auto extensions = getRequiredExtensions();
    std::vector<const char*> extensionNames;
    for (const auto& ext : extensions) {
        extensionNames.push_back(ext.toUtf8().constData());
    }
    
    createInfo.enabledExtensionCount = static_cast<uint32_t>(extensionNames.size());
    createInfo.ppEnabledExtensionNames = extensionNames.data();
    
    // Enable validation layers if requested
    std::vector<const char*> layerNames;
    if (m_validationEnabled) {
        for (const auto& layer : s_validationLayers) {
            layerNames.push_back(layer.toUtf8().constData());
        }
        createInfo.enabledLayerCount = static_cast<uint32_t>(layerNames.size());
        createInfo.ppEnabledLayerNames = layerNames.data();
    } else {
        createInfo.enabledLayerCount = 0;
    }
    
    VkResult result = vkCreateInstance(&createInfo, nullptr, &m_instance);
    if (result != VK_SUCCESS) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanInstanceCreationFailed,
            QString("Failed to create Vulkan instance: %1").arg(result)
        );
    }
    
    VLOG_DEBUG("Vulkan", "Vulkan instance created successfully");
    return Vizion3D::Utils::Result<void>::success();
}

void VulkanContext::cleanup() {
    if (m_device != VK_NULL_HANDLE) {
        vkDestroyDevice(m_device, nullptr);
        m_device = VK_NULL_HANDLE;
    }
    
    if (m_debugMessenger != VK_NULL_HANDLE && m_instance != VK_NULL_HANDLE) {
        auto func = (PFN_vkDestroyDebugUtilsMessengerEXT) vkGetInstanceProcAddr(
            m_instance, "vkDestroyDebugUtilsMessengerEXT");
        if (func != nullptr) {
            func(m_instance, m_debugMessenger, nullptr);
        }
        m_debugMessenger = VK_NULL_HANDLE;
    }
    
    if (m_instance != VK_NULL_HANDLE) {
        vkDestroyInstance(m_instance, nullptr);
        m_instance = VK_NULL_HANDLE;
    }
    
    VLOG_DEBUG("Vulkan", "VulkanContext cleaned up");
}

Vizion3D::Utils::Result<void> VulkanContext::setupDebugMessenger() {
    if (!m_validationEnabled) return Vizion3D::Utils::Result<void>::success();

    VkDebugUtilsMessengerCreateInfoEXT createInfo{};
    createInfo.sType = VK_STRUCTURE_TYPE_DEBUG_UTILS_MESSENGER_CREATE_INFO_EXT;
    createInfo.messageSeverity = VK_DEBUG_UTILS_MESSAGE_SEVERITY_VERBOSE_BIT_EXT |
                                VK_DEBUG_UTILS_MESSAGE_SEVERITY_WARNING_BIT_EXT |
                                VK_DEBUG_UTILS_MESSAGE_SEVERITY_ERROR_BIT_EXT;
    createInfo.messageType = VK_DEBUG_UTILS_MESSAGE_TYPE_GENERAL_BIT_EXT |
                            VK_DEBUG_UTILS_MESSAGE_TYPE_VALIDATION_BIT_EXT |
                            VK_DEBUG_UTILS_MESSAGE_TYPE_PERFORMANCE_BIT_EXT;
    createInfo.pfnUserCallback = debugCallback;
    createInfo.pUserData = nullptr;

    auto func = (PFN_vkCreateDebugUtilsMessengerEXT) vkGetInstanceProcAddr(
        m_instance, "vkCreateDebugUtilsMessengerEXT");

    if (func == nullptr) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanExtensionNotSupported,
            "Debug utils extension not available"
        );
    }

    VkResult result = func(m_instance, &createInfo, nullptr, &m_debugMessenger);
    if (result != VK_SUCCESS) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanInstanceCreationFailed,
            QString("Failed to set up debug messenger: %1").arg(result)
        );
    }

    VLOG_DEBUG("Vulkan", "Debug messenger set up successfully");
    return Vizion3D::Utils::Result<void>::success();
}

bool VulkanContext::checkValidationLayerSupport() const {
    uint32_t layerCount;
    vkEnumerateInstanceLayerProperties(&layerCount, nullptr);

    std::vector<VkLayerProperties> availableLayers(layerCount);
    vkEnumerateInstanceLayerProperties(&layerCount, availableLayers.data());

    for (const auto& layerName : s_validationLayers) {
        bool layerFound = false;

        for (const auto& layerProperties : availableLayers) {
            if (layerName == layerProperties.layerName) {
                layerFound = true;
                break;
            }
        }

        if (!layerFound) {
            VLOG_WARNING("Vulkan", QString("Validation layer not found: %1").arg(layerName));
            return false;
        }
    }

    return true;
}

QStringList VulkanContext::getRequiredExtensions() const {
    QStringList extensions;

    // Add platform-specific surface extensions
#ifdef VK_USE_PLATFORM_MACOS_MVK
    extensions << VK_MVK_MACOS_SURFACE_EXTENSION_NAME;
    extensions << VK_KHR_SURFACE_EXTENSION_NAME;
#elif defined(VK_USE_PLATFORM_WIN32_KHR)
    extensions << VK_KHR_WIN32_SURFACE_EXTENSION_NAME;
    extensions << VK_KHR_SURFACE_EXTENSION_NAME;
#elif defined(VK_USE_PLATFORM_XCB_KHR)
    extensions << VK_KHR_XCB_SURFACE_EXTENSION_NAME;
    extensions << VK_KHR_SURFACE_EXTENSION_NAME;
#endif

    // Add debug extensions if validation is enabled
    if (m_validationEnabled) {
        extensions << VK_EXT_DEBUG_UTILS_EXTENSION_NAME;
    }

    // Add portability enumeration for MoltenVK compatibility
#ifdef VK_USE_PLATFORM_MACOS_MVK
    extensions << VK_KHR_PORTABILITY_ENUMERATION_EXTENSION_NAME;
#endif

    return extensions;
}

Vizion3D::Utils::Result<void> VulkanContext::selectPhysicalDevice() {
    VIZION3D_PROFILE_FUNCTION();

    uint32_t deviceCount = 0;
    vkEnumeratePhysicalDevices(m_instance, &deviceCount, nullptr);

    if (deviceCount == 0) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanPhysicalDeviceNotFound,
            "Failed to find GPUs with Vulkan support"
        );
    }

    std::vector<VkPhysicalDevice> devices(deviceCount);
    vkEnumeratePhysicalDevices(m_instance, &deviceCount, devices.data());

    for (const auto& device : devices) {
        if (isDeviceSuitable(device)) {
            m_physicalDevice = device;
            vkGetPhysicalDeviceProperties(m_physicalDevice, &m_deviceProperties);
            vkGetPhysicalDeviceFeatures(m_physicalDevice, &m_deviceFeatures);
            vkGetPhysicalDeviceMemoryProperties(m_physicalDevice, &m_memoryProperties);

            VLOG_INFO("Vulkan", QString("Selected physical device: %1")
                      .arg(m_deviceProperties.deviceName));
            return Vizion3D::Utils::Result<void>::success();
        }
    }

    return Vizion3D::Utils::Result<void>::error(
        Vizion3D::Utils::ErrorCode::VulkanPhysicalDeviceNotFound,
        "Failed to find a suitable GPU"
    );
}

bool VulkanContext::isDeviceSuitable(VkPhysicalDevice device) const {
    QueueFamilyIndices indices = findQueueFamilies(device);

    // Check if device supports required extensions
    uint32_t extensionCount;
    vkEnumerateDeviceExtensionProperties(device, nullptr, &extensionCount, nullptr);

    std::vector<VkExtensionProperties> availableExtensions(extensionCount);
    vkEnumerateDeviceExtensionProperties(device, nullptr, &extensionCount, availableExtensions.data());

    std::set<QString> requiredExtensions;
    for (const auto& ext : s_deviceExtensions) {
        requiredExtensions.insert(ext);
    }

    for (const auto& extension : availableExtensions) {
        requiredExtensions.erase(extension.extensionName);
    }

    return indices.isComplete() && requiredExtensions.empty();
}

VulkanContext::QueueFamilyIndices VulkanContext::findQueueFamilies(VkPhysicalDevice device) const {
    QueueFamilyIndices indices;

    uint32_t queueFamilyCount = 0;
    vkGetPhysicalDeviceQueueFamilyProperties(device, &queueFamilyCount, nullptr);

    std::vector<VkQueueFamilyProperties> queueFamilies(queueFamilyCount);
    vkGetPhysicalDeviceQueueFamilyProperties(device, &queueFamilyCount, queueFamilies.data());

    int i = 0;
    for (const auto& queueFamily : queueFamilies) {
        if (queueFamily.queueFlags & VK_QUEUE_GRAPHICS_BIT) {
            indices.graphicsFamily = i;
            indices.presentFamily = i; // For now, assume graphics and present are the same
        }

        if (indices.isComplete()) {
            break;
        }

        i++;
    }

    return indices;
}

Vizion3D::Utils::Result<void> VulkanContext::createLogicalDevice() {
    VIZION3D_PROFILE_FUNCTION();

    QueueFamilyIndices indices = findQueueFamilies(m_physicalDevice);

    std::vector<VkDeviceQueueCreateInfo> queueCreateInfos;
    std::set<uint32_t> uniqueQueueFamilies = {indices.graphicsFamily, indices.presentFamily};

    float queuePriority = 1.0f;
    for (uint32_t queueFamily : uniqueQueueFamilies) {
        VkDeviceQueueCreateInfo queueCreateInfo{};
        queueCreateInfo.sType = VK_STRUCTURE_TYPE_DEVICE_QUEUE_CREATE_INFO;
        queueCreateInfo.queueFamilyIndex = queueFamily;
        queueCreateInfo.queueCount = 1;
        queueCreateInfo.pQueuePriorities = &queuePriority;
        queueCreateInfos.push_back(queueCreateInfo);
    }

    VkPhysicalDeviceFeatures deviceFeatures{};
    // Enable features we need
    deviceFeatures.samplerAnisotropy = VK_TRUE;

    VkDeviceCreateInfo createInfo{};
    createInfo.sType = VK_STRUCTURE_TYPE_DEVICE_CREATE_INFO;
    createInfo.queueCreateInfoCount = static_cast<uint32_t>(queueCreateInfos.size());
    createInfo.pQueueCreateInfos = queueCreateInfos.data();
    createInfo.pEnabledFeatures = &deviceFeatures;

    // Device extensions
    std::vector<const char*> extensionNames;
    for (const auto& ext : s_deviceExtensions) {
        extensionNames.push_back(ext.toUtf8().constData());
    }
    createInfo.enabledExtensionCount = static_cast<uint32_t>(extensionNames.size());
    createInfo.ppEnabledExtensionNames = extensionNames.data();

    // Validation layers (for compatibility with older implementations)
    std::vector<const char*> layerNames;
    if (m_validationEnabled) {
        for (const auto& layer : s_validationLayers) {
            layerNames.push_back(layer.toUtf8().constData());
        }
        createInfo.enabledLayerCount = static_cast<uint32_t>(layerNames.size());
        createInfo.ppEnabledLayerNames = layerNames.data();
    } else {
        createInfo.enabledLayerCount = 0;
    }

    VkResult result = vkCreateDevice(m_physicalDevice, &createInfo, nullptr, &m_device);
    if (result != VK_SUCCESS) {
        return Vizion3D::Utils::Result<void>::error(
            Vizion3D::Utils::ErrorCode::VulkanDeviceCreationFailed,
            QString("Failed to create logical device: %1").arg(result)
        );
    }

    // Get queue handles
    vkGetDeviceQueue(m_device, indices.graphicsFamily, 0, &m_graphicsQueue);
    vkGetDeviceQueue(m_device, indices.presentFamily, 0, &m_presentQueue);

    m_graphicsQueueFamily = indices.graphicsFamily;
    m_presentQueueFamily = indices.presentFamily;

    VLOG_DEBUG("Vulkan", "Logical device created successfully");
    return Vizion3D::Utils::Result<void>::success();
}

} // namespace Vizion3D::UI
