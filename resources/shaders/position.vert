#version 450
// Vulkan SPIR-V - Vizion3D standard

layout(binding = 0) uniform UniformBufferObject {
    mat4 modelViewProjection;
    mat4 modelView;
    vec4 color;
} ubo;

layout(location = 0) in vec3 position;
layout(location = 1) in vec3 normal;

layout(location = 0) out vec3 fragNormal;
layout(location = 1) out vec3 fragPosition;
layout(location = 2) out vec4 fragColor;

void main() {
    gl_Position = ubo.modelViewProjection * vec4(position, 1.0);
    fragNormal = mat3(ubo.modelView) * normal;
    fragPosition = vec3(ubo.modelView * vec4(position, 1.0));
    fragColor = ubo.color;
}
