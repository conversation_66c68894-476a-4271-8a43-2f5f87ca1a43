#version 450
// Vulkan SPIR-V - Vizion3D standard

layout(location = 0) in vec3 fragNormal;
layout(location = 1) in vec3 fragPosition;
layout(location = 2) in vec4 fragColor;

layout(location = 0) out vec4 outColor;

void main() {
    // Fixed light position for simplicity (can be moved to UBO later)
    vec3 lightPosition = vec3(10.0, 10.0, 10.0);

    // Ambient light
    float ambientStrength = 0.3;
    vec3 ambient = ambientStrength * vec3(1.0, 1.0, 1.0);

    // Diffuse light
    vec3 norm = normalize(fragNormal);
    vec3 lightDir = normalize(lightPosition - fragPosition);
    float diff = max(dot(norm, lightDir), 0.0);
    vec3 diffuse = diff * vec3(1.0, 1.0, 1.0);

    // Specular light
    float specularStrength = 0.5;
    vec3 viewDir = normalize(-fragPosition);
    vec3 reflectDir = reflect(-lightDir, norm);
    float spec = pow(max(dot(viewDir, reflectDir), 0.0), 32);
    vec3 specular = specularStrength * spec * vec3(1.0, 1.0, 1.0);

    // Final color
    vec3 result = (ambient + diffuse + specular) * fragColor.rgb;
    outColor = vec4(result, fragColor.a);
}
